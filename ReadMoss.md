请执行以下任务来帮助我设计后续实验：
1. **全面阅读和分析**：
   - 仔细阅读 `./模型(实验)记录.csv`文件的完整内容
   - 仔细阅读该文件表格中提到的所有相关配置文件和实验记录
   - 重点查看所有训练的实验数据曲线图(和*.log同名的png图片，理解训练趋势)，表格Train-Data(TimeID)表示实验记录的日志存储文件夹名称，路径在/media/pyl/WD_Blue_1T/All_proj/TimeConv_classify/mmaction2_main0808/tools/work_dirs/pose_rgb_fusion文件夹中，文件夹名称和表格Train-Data(TimeID)一致
   - 结合训练曲线理解当前的实验进展、遇到的问题和需求
   - [基础说明]
训练框架即为当前的项目路径 /media/pyl/WD_Blue_1T/All_proj/TimeConv_classify/mmaction2-main0808， 训练采用的4卡(单卡用于测试流程)，
模型使用的是 mmaction/models/recognizers/multimodal_recognizer.py
训练脚本：/media/pyl/WD_Blue_1T/All_proj/TimeConv_classify/mmaction2-main0808/tools/train_m2.py

2. **数据结构理解**：
   - 根据 `./模型(实验)记录.csv` 中的场景描述和样本描述，理解样本的结构和格式,重点理解 重要说明[多模态特征具有互补性]
   - 如有必要，可从 `/media/pyl/WD_Blue_1T/All_proj/pose_rgb` 中的示例样本(从完整的训练和测试数据中随机抽出了部分样本作为参考,每个pkl所在的文件夹包含的所有内容为1个样本(包含1个pkl记录骨骼数据, 3张裁剪后的RGB图片, 1个avi视频标签用于判断样本属于哪个类别))中，抽取1～2个数据了解,主要关注样本中的3张jpg图像；

3. **实验设计**：
   - 基于当前的实验状态和问题，提出具体的后续实验方案，重点关注实验13和14取得的好成果及其原因
   - 为每个实验方案提供详细的实施步骤和理论依据
   - 设计方案时，先从训练方式上优化，不改动目前的数据和模型(可以仅提出参考建议)；
   - 注意，后续训练数据会逐步增加，因此方案中不要出现样本的具体数量，而是用变量代替

4. **重要约束条件**：
   - **严格禁止**修改实验4和实验13（Baseline）使用的任何文件，包括：
     - 模型文件
     - 数据集相关文件
     - 配置文件 
     - 训练代码
   - 所有新的实验和改动必须通过**创建新文件**的方式实现
   - 确保不影响现有的基线(实验4和实验13)

请先完成文件阅读和分析，然后提供具体的实验设计建议，目标是提高异常检出率(0.98以上)和降低正常误判率(1%以内)。
涉及到执行环境相关，使用conda activate torch环境


(1) if num_frames < (clip_len / 2) : 改成从前向后拷贝插值,再通过重复首帧补齐clip_len，例如原来的帧是[0,1,2,3,....9], 插入后[0,0,1,1,2,2,3,3,....,9,9], 然后再在开头补0, [0,0,0...] + [0,0,1,1,2,2,....]
(2)elif (clip_len / 2)   < num_frames < clip_len:  改成从前向后拷贝插值(同上),补齐即停；
(3)elif (clip_len * 2) > num_frames > clip_len:  取从后像前的(clip_len-5)帧，其余帧均匀抽取5帧；
(4)else: 从后像前截取 (clip_len * 2)帧，截取帧的处理方法同(3)



