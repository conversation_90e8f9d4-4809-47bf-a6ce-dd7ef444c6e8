# -*- coding: utf-8 -*-
"""
配置路径解析器 - 处理嵌套字典的路径访问
Created by: Moss
Date: 2025-08-25
"""

from typing import Any, Dict, List, Union
import logging

logger = logging.getLogger(__name__)


class ConfigPathResolver:
    """
    配置路径解析器
    
    支持通过路径字符串访问和修改嵌套字典
    例如: 'model.fusion_neck.dropout' -> config['model']['fusion_neck']['dropout']
    """
    
    @staticmethod
    def get_value(config: Dict[str, Any], path: str) -> Any:
        """
        根据路径获取配置值
        
        Args:
            config: 配置字典
            path: 路径字符串，如 'model.fusion_neck.dropout'
            
        Returns:
            配置值
            
        Raises:
            KeyError: 路径不存在
        """
        keys = path.split('.')
        current = config
        
        try:
            for key in keys:
                if isinstance(current, dict):
                    current = current[key]
                else:
                    raise KeyError(f"Cannot access key '{key}' in non-dict object at path '{path}'")
            return current
        except KeyError as e:
            raise KeyError(f"Path '{path}' not found in config: {str(e)}")
    
    @staticmethod
    def set_value(config: Dict[str, Any], path: str, value: Any) -> None:
        """
        根据路径设置配置值
        
        Args:
            config: 配置字典
            path: 路径字符串，如 'model.fusion_neck.dropout'
            value: 要设置的值
        """
        keys = path.split('.')
        current = config
        
        # 导航到倒数第二层
        for key in keys[:-1]:
            if key not in current:
                current[key] = {}
            elif not isinstance(current[key], dict):
                raise ValueError(f"Cannot create nested path '{path}': '{key}' is not a dict")
            current = current[key]
        
        # 设置最后一层的值
        final_key = keys[-1]
        current[final_key] = value
        
        logger.debug(f"Set config path '{path}' = {value}")
    
    @staticmethod
    def path_exists(config: Dict[str, Any], path: str) -> bool:
        """
        检查路径是否存在
        
        Args:
            config: 配置字典
            path: 路径字符串
            
        Returns:
            路径是否存在
        """
        try:
            ConfigPathResolver.get_value(config, path)
            return True
        except KeyError:
            return False
    
    @staticmethod
    def create_nested_dict(path: str, value: Any) -> Dict[str, Any]:
        """
        根据路径创建嵌套字典
        
        Args:
            path: 路径字符串
            value: 最终值
            
        Returns:
            嵌套字典
        """
        keys = path.split('.')
        result = {}
        current = result
        
        for key in keys[:-1]:
            current[key] = {}
            current = current[key]
        
        current[keys[-1]] = value
        return result
    
    @staticmethod
    def merge_configs(base_config: Dict[str, Any], override_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        深度合并配置字典
        
        Args:
            base_config: 基础配置
            override_config: 覆盖配置
            
        Returns:
            合并后的配置
        """
        def deep_merge(base: Dict[str, Any], override: Dict[str, Any]) -> Dict[str, Any]:
            result = base.copy()
            
            for key, value in override.items():
                if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                    result[key] = deep_merge(result[key], value)
                else:
                    result[key] = value
            
            return result
        
        return deep_merge(base_config, override_config)
    
    @staticmethod
    def get_all_paths(config: Dict[str, Any], prefix: str = '') -> List[str]:
        """
        获取配置中所有可能的路径
        
        Args:
            config: 配置字典
            prefix: 路径前缀
            
        Returns:
            所有路径的列表
        """
        paths = []
        
        for key, value in config.items():
            current_path = f"{prefix}.{key}" if prefix else key
            
            if isinstance(value, dict):
                # 递归处理嵌套字典
                paths.extend(ConfigPathResolver.get_all_paths(value, current_path))
            else:
                # 叶子节点
                paths.append(current_path)
        
        return paths
    
    @staticmethod
    def validate_path_format(path: str) -> bool:
        """
        验证路径格式
        
        Args:
            path: 路径字符串
            
        Returns:
            是否为有效格式
        """
        if not path or not isinstance(path, str):
            return False
        
        # 不能以点开始或结束
        if path.startswith('.') or path.endswith('.'):
            return False
        
        # 不能包含连续的点
        if '..' in path:
            return False
        
        # 不能包含空的键名
        keys = path.split('.')
        if any(not key.strip() for key in keys):
            return False
        
        return True


class ConfigUpdater:
    """
    配置更新器 - 基于超参数映射更新配置
    """
    
    def __init__(self, hyperparameter_mapping: Dict[str, Dict[str, Any]]):
        """
        初始化配置更新器
        
        Args:
            hyperparameter_mapping: 超参数映射配置
        """
        self.mapping = hyperparameter_mapping
        self.resolver = ConfigPathResolver()
    
    def update_config_with_hyperparams(self, config: Dict[str, Any], 
                                     hyperparams: Dict[str, Any]) -> Dict[str, Any]:
        """
        使用超参数更新配置
        
        Args:
            config: 基础配置字典
            hyperparams: 超参数值字典
            
        Returns:
            更新后的配置字典
        """
        updated_config = config.copy()
        
        for param_name, param_value in hyperparams.items():
            if param_name not in self.mapping:
                logger.warning(f"Unknown hyperparameter: {param_name}")
                continue
            
            config_path = self.mapping[param_name]['config_path']
            
            try:
                self.resolver.set_value(updated_config, config_path, param_value)
                logger.info(f"Updated {config_path} = {param_value}")
            except Exception as e:
                logger.error(f"Failed to update {config_path}: {str(e)}")
        
        return updated_config
    
    def validate_hyperparams(self, hyperparams: Dict[str, Any]) -> List[str]:
        """
        验证超参数
        
        Args:
            hyperparams: 超参数字典
            
        Returns:
            错误信息列表
        """
        errors = []
        
        for param_name, param_value in hyperparams.items():
            if param_name not in self.mapping:
                errors.append(f"Unknown hyperparameter: {param_name}")
                continue
            
            config_path = self.mapping[param_name]['config_path']
            if not self.resolver.validate_path_format(config_path):
                errors.append(f"Invalid config path for {param_name}: {config_path}")
        
        return errors
    
    def get_current_values(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取当前配置中超参数的值
        
        Args:
            config: 配置字典
            
        Returns:
            当前超参数值字典
        """
        current_values = {}
        
        for param_name, param_config in self.mapping.items():
            config_path = param_config['config_path']
            
            try:
                current_value = self.resolver.get_value(config, config_path)
                current_values[param_name] = current_value
            except KeyError:
                logger.warning(f"Config path not found: {config_path}")
                current_values[param_name] = None
        
        return current_values


# 使用示例和测试
if __name__ == '__main__':
    # 测试配置路径解析器
    test_config = {
        'model': {
            'fusion_neck': {
                'dropout': 0.5,
                'fusion_dim': 512
            },
            'cls_head': {
                'num_classes': 3
            }
        },
        'optim_wrapper': {
            'optimizer': {
                'lr': 0.001,
                'weight_decay': 0.0001
            }
        }
    }
    
    resolver = ConfigPathResolver()
    
    # 测试获取值
    print("Testing get_value:")
    print(f"model.fusion_neck.dropout = {resolver.get_value(test_config, 'model.fusion_neck.dropout')}")
    print(f"optim_wrapper.optimizer.lr = {resolver.get_value(test_config, 'optim_wrapper.optimizer.lr')}")
    
    # 测试设置值
    print("\nTesting set_value:")
    resolver.set_value(test_config, 'model.fusion_neck.dropout', 0.7)
    resolver.set_value(test_config, 'new.nested.param', 'test_value')
    print(f"Updated dropout = {resolver.get_value(test_config, 'model.fusion_neck.dropout')}")
    print(f"New param = {resolver.get_value(test_config, 'new.nested.param')}")
    
    # 测试获取所有路径
    print("\nAll paths in config:")
    all_paths = resolver.get_all_paths(test_config)
    for path in sorted(all_paths):
        print(f"  {path}")
