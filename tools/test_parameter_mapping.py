#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
参数映射系统测试脚本
Created by: Moss
Date: 2025-08-25
"""

import os
import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from configs.recognition.Multimodal.optuna_templates.hyperparameter_mapping import (
    HYPERPARAMETER_MAPPING, PARAMETER_GROUPS, get_hyperparameters_by_group
)
from tools.config_path_resolver import ConfigPathResolver, ConfigUpdater

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_hyperparameter_mapping():
    """测试超参数映射配置"""
    logger.info("🔍 测试超参数映射配置")
    
    try:
        # 测试获取所有参数组
        logger.info(f"总共定义了 {len(HYPERPARAMETER_MAPPING)} 个超参数")
        logger.info(f"定义了 {len(PARAMETER_GROUPS)} 个参数组")
        
        # 测试高优先级参数组
        high_priority_params = get_hyperparameters_by_group('high_priority')
        logger.info(f"高优先级参数组包含 {len(high_priority_params)} 个参数:")
        
        for param_name, param_config in high_priority_params.items():
            config_path = param_config['config_path']
            description = param_config['description']
            logger.info(f"  - {param_name}: {config_path} ({description})")
        
        logger.info("✅ 超参数映射配置测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 超参数映射配置测试失败: {str(e)}")
        return False


def test_config_path_resolver():
    """测试配置路径解析器"""
    logger.info("🔍 测试配置路径解析器")
    
    try:
        resolver = ConfigPathResolver()
        
        # 创建测试配置
        test_config = {
            'model': {
                'fusion_neck': {
                    'dropout': 0.5,
                    'fusion_dim': 512
                }
            },
            'optim_wrapper': {
                'optimizer': {
                    'lr': 0.001,
                    'weight_decay': 0.0001
                },
                'paramwise_cfg': {
                    'custom_keys': {
                        'image_backbone': {'lr_mult': 0.1}
                    }
                }
            }
        }
        
        # 测试获取值
        dropout_value = resolver.get_value(test_config, 'model.fusion_neck.dropout')
        lr_value = resolver.get_value(test_config, 'optim_wrapper.optimizer.lr')
        lr_mult_value = resolver.get_value(test_config, 'optim_wrapper.paramwise_cfg.custom_keys.image_backbone.lr_mult')
        
        logger.info(f"获取 model.fusion_neck.dropout = {dropout_value}")
        logger.info(f"获取 optim_wrapper.optimizer.lr = {lr_value}")
        logger.info(f"获取 optim_wrapper.paramwise_cfg.custom_keys.image_backbone.lr_mult = {lr_mult_value}")
        
        # 测试设置值
        resolver.set_value(test_config, 'model.fusion_neck.dropout', 0.7)
        resolver.set_value(test_config, 'new.nested.param', 'test_value')
        
        new_dropout = resolver.get_value(test_config, 'model.fusion_neck.dropout')
        new_param = resolver.get_value(test_config, 'new.nested.param')
        
        logger.info(f"设置后 model.fusion_neck.dropout = {new_dropout}")
        logger.info(f"新参数 new.nested.param = {new_param}")
        
        # 测试路径验证
        valid_paths = [
            'model.fusion_neck.dropout',
            'optim_wrapper.optimizer.lr',
            'train_dataloader.batch_size'
        ]
        
        invalid_paths = [
            '.model.fusion_neck.dropout',
            'model.fusion_neck.dropout.',
            'model..fusion_neck.dropout',
            ''
        ]
        
        for path in valid_paths:
            if not resolver.validate_path_format(path):
                raise ValueError(f"Valid path marked as invalid: {path}")
        
        for path in invalid_paths:
            if resolver.validate_path_format(path):
                raise ValueError(f"Invalid path marked as valid: {path}")
        
        logger.info("✅ 配置路径解析器测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 配置路径解析器测试失败: {str(e)}")
        return False


def test_config_updater():
    """测试配置更新器"""
    logger.info("🔍 测试配置更新器")
    
    try:
        # 获取高优先级参数映射
        param_mapping = get_hyperparameters_by_group('high_priority')
        updater = ConfigUpdater(param_mapping)
        
        # 创建基础配置
        base_config = {
            'model': {
                'fusion_neck': {
                    'dropout': 0.5,
                    'fusion_dim': 512
                }
            },
            'optim_wrapper': {
                'optimizer': {
                    'lr': 0.001,
                    'weight_decay': 0.0001
                },
                'paramwise_cfg': {
                    'custom_keys': {
                        'image_backbone': {'lr_mult': 0.1},
                        'cls_head': {'lr_mult': 1.0}
                    }
                }
            },
            'train_dataloader': {
                'batch_size': 4
            }
        }
        
        # 测试超参数
        test_hyperparams = {
            'base_lr': 0.002,
            'image_backbone_lr_mult': 0.15,
            'cls_head_lr_mult': 1.5,
            'fusion_neck_dropout': 0.6,
            'weight_decay': 0.0002,
            'batch_size': 8
        }
        
        # 更新配置
        updated_config = updater.update_config_with_hyperparams(base_config, test_hyperparams)
        
        # 验证更新结果
        resolver = ConfigPathResolver()
        
        expected_updates = {
            'optim_wrapper.optimizer.lr': 0.002,
            'optim_wrapper.paramwise_cfg.custom_keys.image_backbone.lr_mult': 0.15,
            'optim_wrapper.paramwise_cfg.custom_keys.cls_head.lr_mult': 1.5,
            'model.fusion_neck.dropout': 0.6,
            'optim_wrapper.optimizer.weight_decay': 0.0002,
            'train_dataloader.batch_size': 8
        }
        
        for path, expected_value in expected_updates.items():
            actual_value = resolver.get_value(updated_config, path)
            if actual_value != expected_value:
                raise ValueError(f"Update failed for {path}: expected {expected_value}, got {actual_value}")
            logger.info(f"✓ {path} = {actual_value}")
        
        # 测试验证功能
        errors = updater.validate_hyperparams(test_hyperparams)
        if errors:
            raise ValueError(f"Validation errors: {errors}")
        
        # 测试获取当前值
        current_values = updater.get_current_values(updated_config)
        logger.info(f"当前配置中的超参数值: {len(current_values)} 个")
        
        logger.info("✅ 配置更新器测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 配置更新器测试失败: {str(e)}")
        return False


def test_parameter_groups():
    """测试参数组功能"""
    logger.info("🔍 测试参数组功能")
    
    try:
        # 测试所有参数组
        for group_name in PARAMETER_GROUPS.keys():
            group_params = get_hyperparameters_by_group(group_name)
            logger.info(f"参数组 '{group_name}': {len(group_params)} 个参数")
            
            # 验证所有参数都在映射中
            for param_name in PARAMETER_GROUPS[group_name]:
                if param_name not in HYPERPARAMETER_MAPPING:
                    raise ValueError(f"Parameter {param_name} in group {group_name} not found in mapping")
        
        # 测试无效参数组
        try:
            get_hyperparameters_by_group('invalid_group')
            raise ValueError("Should have raised error for invalid group")
        except ValueError as e:
            if "Unknown parameter group" not in str(e):
                raise
        
        logger.info("✅ 参数组功能测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 参数组功能测试失败: {str(e)}")
        return False


def main():
    """主函数"""
    logger.info("=" * 80)
    logger.info("参数映射系统测试")
    logger.info("=" * 80)
    
    tests = [
        ("超参数映射配置测试", test_hyperparameter_mapping),
        ("配置路径解析器测试", test_config_path_resolver),
        ("配置更新器测试", test_config_updater),
        ("参数组功能测试", test_parameter_groups),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n🔍 运行测试: {test_name}")
        try:
            if test_func():
                passed += 1
            else:
                logger.error(f"测试失败: {test_name}")
        except Exception as e:
            logger.error(f"测试异常: {test_name} - {str(e)}")
    
    logger.info("\n" + "=" * 80)
    logger.info(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        logger.info("🎉 所有参数映射系统测试通过！")
        logger.info("新的参数映射系统已就绪，可以轻松添加任意配置参数到优化空间")
    else:
        logger.error("❌ 部分测试失败，请检查并修复问题")
    
    logger.info("=" * 80)


if __name__ == '__main__':
    main()
