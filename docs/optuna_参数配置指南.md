# Optuna超参数配置指南

## 概述

本文档详细说明如何配置和修改Optuna超参数优化框架中的搜索空间。

## 1. 超参数搜索空间配置位置

### ⭐ 新的参数映射系统（推荐）
- **参数映射配置**: `configs/recognition/Multimodal/optuna_templates/hyperparameter_mapping.py`
- **配置路径解析**: `tools/config_path_resolver.py`
- **自动配置生成**: `tools/optuna_trainer.py` 基于映射自动生成

### 旧的配置方式（已弃用）
- ~~**核心配置**: `tools/optuna_trainer.py` 中的 `suggest_hyperparameters` 方法~~
- ~~**辅助配置**: `tools/optuna_config_generator.py` 中的 `get_hyperparameter_spaces` 方法~~

**重要**：新系统解决了参数映射不清晰的问题，现在可以直接将配置文件中的任意参数路径添加到优化空间！

## 2. 新的参数映射系统

### 参数映射配置格式
每个超参数在 `hyperparameter_mapping.py` 中定义为：
```python
'parameter_name': {
    'config_path': '配置文件中的路径',  # 如 'model.fusion_neck.dropout'
    'type': 'suggest类型',              # 如 'suggest_float'
    'params': {'low': 0.1, 'high': 0.8},  # suggest方法的参数
    'description': '参数描述'
}
```

### 当前支持的超参数（基于实际配置路径）

#### 高优先级超参数（对性能影响最大）

#### 学习率相关
```python
# 基础学习率 -> optim_wrapper.optimizer.lr
'base_lr': {
    'config_path': 'optim_wrapper.optimizer.lr',
    'type': 'suggest_float',
    'params': {'low': 1e-5, 'high': 1e-2, 'log': True}
}

# 图像骨干网络学习率倍数 -> optim_wrapper.paramwise_cfg.custom_keys.image_backbone.lr_mult
'image_backbone_lr_mult': {
    'config_path': 'optim_wrapper.paramwise_cfg.custom_keys.image_backbone.lr_mult',
    'type': 'suggest_float',
    'params': {'low': 0.05, 'high': 0.2}
}

# 融合模块学习率倍数 -> optim_wrapper.paramwise_cfg.custom_keys.fusion_neck.lr_mult
'fusion_neck_lr_mult': {
    'config_path': 'optim_wrapper.paramwise_cfg.custom_keys.fusion_neck.lr_mult',
    'type': 'suggest_float',
    'params': {'low': 0.5, 'high': 2.0}
}
```

#### 正则化参数
```python
# 融合模块dropout率
'fusion_dropout': trial.suggest_float('fusion_dropout', 0.3, 0.7)

# 权重衰减 (对数尺度)
'weight_decay': trial.suggest_float('weight_decay', 1e-5, 1e-3, log=True)
```

#### 训练策略
```python
# 批次大小 (分类选择)
'batch_size': trial.suggest_categorical('batch_size', [2, 4, 6, 8])
```

### 中优先级超参数

#### 模型结构
```python
# 融合特征维度 (分类选择)
'fusion_dim': trial.suggest_categorical('fusion_dim', [256, 384, 512, 768, 1024])

# 预热轮数
'warmup_epochs': trial.suggest_int('warmup_epochs', 3, 10)
```

## 3. 如何使用新的参数映射系统

### 3.1 查看可用的参数组
```bash
# 列出所有参数组
python tools/optuna_trainer.py --list-groups

# 使用特定参数组进行优化
python tools/optuna_trainer.py --optimization-group high_priority --n-trials 10
```

### 3.2 修改现有参数的搜索范围

**位置**: `configs/recognition/Multimodal/optuna_templates/hyperparameter_mapping.py`

**示例**: 修改学习率倍数的搜索范围
```python
# 在 HYPERPARAMETER_MAPPING 中找到对应参数
'cls_head_lr_mult': {
    'config_path': 'optim_wrapper.paramwise_cfg.custom_keys.cls_head.lr_mult',
    'type': 'suggest_float',
    'params': {'low': 0.8, 'high': 2.0},  # 原始范围
    'description': '分类头学习率倍数'
}

# 修改为更大的搜索范围
'cls_head_lr_mult': {
    'config_path': 'optim_wrapper.paramwise_cfg.custom_keys.cls_head.lr_mult',
    'type': 'suggest_float',
    'params': {'low': 0.5, 'high': 3.0},  # 扩大范围
    'description': '分类头学习率倍数'
}
```

### 3.3 添加新的超参数（超级简单！）

#### 一步完成：在参数映射中添加新参数
**文件**: `configs/recognition/Multimodal/optuna_templates/hyperparameter_mapping.py`

**示例1**: 添加分类头的dropout参数
```python
# 在 HYPERPARAMETER_MAPPING 中添加
'cls_head_dropout': {
    'config_path': 'model.cls_head.dropout',  # 配置文件中的实际路径
    'type': 'suggest_float',
    'params': {'low': 0.1, 'high': 0.8},
    'description': '分类头dropout率'
}
```

**示例2**: 添加数据增强参数
```python
'mixup_alpha': {
    'config_path': 'train_pipeline.2.alpha',  # 假设mixup是第3个变换
    'type': 'suggest_float',
    'params': {'low': 0.1, 'high': 1.0},
    'description': 'Mixup混合强度'
}
```

**示例3**: 添加模型结构参数
```python
'hidden_dim': {
    'config_path': 'model.cls_head.hidden_dim',
    'type': 'suggest_categorical',
    'params': {'choices': [256, 512, 1024, 2048]},
    'description': '分类头隐藏层维度'
}
```

#### 就这么简单！无需修改任何其他代码
- ✅ 自动参与优化
- ✅ 自动生成配置
- ✅ 自动应用到训练

## 4. 具体配置示例

### 4.1 学习率倍数配置示例

根据您选中的代码 `'cls_head': dict(lr_mult=1.0)`，这是在原始配置文件中的默认设置。

**当前优化配置**:
```python
# 在optuna_trainer.py中
hyperparams['cls_head_lr_mult'] = trial.suggest_float('cls_head_lr_mult', 0.8, 2.0)

# 应用到配置中
'cls_head': dict(lr_mult={hyperparams['cls_head_lr_mult']})
```

**如果要修改搜索范围**:
```python
# 扩大搜索范围
hyperparams['cls_head_lr_mult'] = trial.suggest_float('cls_head_lr_mult', 0.5, 3.0)

# 或使用分类选择
hyperparams['cls_head_lr_mult'] = trial.suggest_categorical('cls_head_lr_mult', [0.5, 1.0, 1.5, 2.0, 2.5])
```

### 4.2 添加新模块的学习率倍数

**步骤1**: 在 `suggest_hyperparameters` 中添加
```python
hyperparams['pose_backbone_lr_mult'] = trial.suggest_float('pose_backbone_lr_mult', 0.5, 2.0)
```

**步骤2**: 在配置生成中应用
```python
custom_keys={{
    'image_backbone': dict(lr_mult={hyperparams['image_backbone_lr_mult']}),
    'pose_backbone': dict(lr_mult={hyperparams['pose_backbone_lr_mult']}),  # 修改这里
    'fusion_neck': dict(lr_mult={hyperparams['fusion_neck_lr_mult']}),
    'cls_head': dict(lr_mult={hyperparams['cls_head_lr_mult']})
}}
```

## 5. 参数类型说明

### 5.1 连续参数 (suggest_float)
```python
# 普通范围
trial.suggest_float('param_name', low, high)

# 对数尺度 (适合学习率等跨度大的参数)
trial.suggest_float('param_name', low, high, log=True)
```

### 5.2 整数参数 (suggest_int)
```python
trial.suggest_int('param_name', low, high)
```

### 5.3 分类参数 (suggest_categorical)
```python
trial.suggest_categorical('param_name', [choice1, choice2, choice3])
```

## 6. 测试新配置

### 6.1 验证配置正确性
```bash
# 运行配置验证测试
python tools/test_optuna_mini.py --mode validate
```

### 6.2 小规模测试
```bash
# 运行少量试验测试新配置
python tools/optuna_trainer.py --n-trials 3
```

### 6.3 查看参数重要性
```bash
# 分析参数重要性
python tools/optuna_analyzer.py --study-name your_study --action importance
```

## 7. 最佳实践

### 7.1 参数搜索范围设置
- **学习率**: 使用对数尺度，范围通常为 [1e-5, 1e-2]
- **学习率倍数**: 通常在 [0.1, 2.0] 范围内
- **Dropout**: 通常在 [0.1, 0.7] 范围内
- **批次大小**: 根据GPU内存限制选择合适的选项

### 7.2 优化策略
1. **先优化高影响参数**: 学习率、正则化参数
2. **再优化结构参数**: 模型维度、层数等
3. **最后优化训练策略**: 批次大小、调度器参数

### 7.3 实验管理
- 使用有意义的study名称
- 定期保存和分析结果
- 根据参数重要性调整搜索空间

## 8. 常见问题

### Q1: 如何知道哪些参数可以优化？
A1: 查看原始配置文件 `configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion.py`，任何数值型参数都可以加入优化。

### Q2: 参数搜索范围如何确定？
A2: 
- 参考论文中的典型值范围
- 从原始配置值的0.1倍到10倍开始
- 根据初步实验结果调整

### Q3: 如何处理参数之间的依赖关系？
A3: 在 `suggest_hyperparameters` 方法中添加条件逻辑：
```python
if hyperparams['use_feature_a']:
    hyperparams['feature_a_param'] = trial.suggest_float('feature_a_param', 0.1, 1.0)
else:
    hyperparams['feature_a_param'] = 0.0
```

---

**更新时间**: 2025-08-23  
**版本**: v1.0
